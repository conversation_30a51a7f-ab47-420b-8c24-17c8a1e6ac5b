import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {post, requestBody, response} from '@loopback/rest';
import {
  FormCollectionRepository,
  NewMetricRepository,
  UserProfileRepository
} from '../repositories';
import {UserProfileController} from './user-profile.controller';

interface DcfAssignment {
  dcfId: string;
  locationId: number;
  level: number;
  start_date: string;
  end_date: string | null;
  frequency: number;
  reportingPeriods?: ReportingPeriod;
}

interface ReportingPeriod {
  valid_periods: string[];
  data_granularity: string;
}

interface ReportingFrequencyInput {
  reporting_frequency: string;
  reporting_period_from?: string;
  reporting_period_to?: string;
  reporting_period: string;
  year: string | number | number[];
  entity: string[];
}

const frequencyMapping: {[key: number]: string} = {
  1: 'monthly',
  2: 'bi-monthly',
  3: 'quarterly',
  4: 'yearly',
  5: 'half-yearly'
};

interface FrequencyWeight {
  [key: string]: number;
  monthly: number;
  'bi-monthly': number;
  quarterly: number;
  'half-yearly': number;
  yearly: number;
  custom: number;
}

export class SttReportController {
  constructor(
    @inject('controllers.UserProfileController')
    public userProfileController: UserProfileController,
    @repository(FormCollectionRepository)
    public formCollectionRepository: FormCollectionRepository,

    @repository(NewMetricRepository)
    public newMetricRepository: NewMetricRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository
  ) { }

  // Add frequency weights as a class property
  private frequencyWeights: FrequencyWeight = {
    monthly: 1,
    'bi-monthly': 2,
    quarterly: 3,
    'half-yearly': 4,
    yearly: 5,
    custom: 1
  };

  private getFrequencyFromCode(code: number): string {
    switch (code) {
      case 1: return 'monthly';
      case 2: return 'bi-monthly';
      case 3: return 'quarterly';
      case 4: return 'yearly';
      case 5: return 'half-yearly';
      case 6: return 'custom';
      default: throw new Error(`Invalid frequency code: ${code}`);
    }
  }

  private validateFrequencyCompatibility(
    requestedFrequency: string,
    assignment: any,
    customPeriodFrom?: string,
    customPeriodTo?: string,
    year?: string | number | number[]
  ): {isValid: boolean; incompatibleReason?: string} {
    // First validate that the requested period falls within the assignment period
    const assignmentStart = new Date(assignment.start_date);
    const assignmentEnd = assignment.end_date ? new Date(assignment.end_date) : new Date();

    // Normalize the requested frequency
    const normalizedFrequency = requestedFrequency.toLowerCase();

    // Different date range validation for custom vs non-custom periods
    if (normalizedFrequency === 'custom') {
      // For custom periods, validate using the exact from/to dates
      if (!customPeriodFrom || !customPeriodTo) {
        return {
          isValid: false,
          incompatibleReason: 'Custom period requires both start and end dates'
        };
      }

      const requestStart = new Date(customPeriodFrom);
      const requestEnd = new Date(customPeriodTo);

      // Set times to start and end of day for accurate comparison
      requestStart.setUTCHours(0, 0, 0, 0);
      requestEnd.setUTCHours(23, 59, 59, 999);
      assignmentStart.setUTCHours(0, 0, 0, 0);
      assignmentEnd.setUTCHours(23, 59, 59, 999);

      if (requestStart < assignmentStart || requestEnd > assignmentEnd) {
        return {
          isValid: false,
          incompatibleReason: `Requested period (${customPeriodFrom} to ${customPeriodTo}) must fall within the assignment period (${assignment.start_date.split('T')[0]} to ${assignment.end_date ? assignment.end_date.split('T')[0] : 'present'})`
        };
      }
    } else {
      // For non-custom frequencies, validate using the year
      if (!year) {
        return {
          isValid: false,
          incompatibleReason: 'Year is required for non-custom periods'
        };
      }

      // Handle year as either string, number, or array
      let requestedYear: number;
      if (Array.isArray(year)) {
        // For array, use the first year for validation
        requestedYear = year[0];
      } else if (typeof year === 'number') {
        requestedYear = year;
      } else {
        requestedYear = parseInt(year);
      }

      const assignmentStartYear = assignmentStart.getUTCFullYear();
      const assignmentEndYear = assignmentEnd.getUTCFullYear();

      if (requestedYear < assignmentStartYear || requestedYear > assignmentEndYear) {
        return {
          isValid: false,
          incompatibleReason: `Requested year ${requestedYear} must fall within the assignment period (${assignmentStartYear} to ${assignmentEndYear})`
        };
      }
    }

    // Get assignment frequency details
    const assignmentFreq = this.getFrequencyFromCode(assignment.frequency);

    // Define compatibility matrix - assignment frequency -> supported requested frequencies
    const compatibilityMatrix: {[key: string]: string[]} = {
      'monthly': ['monthly', 'bi-monthly', 'quarterly', 'half-yearly', 'yearly'],  // Monthly can support all
      'bi-monthly': ['bi-monthly', 'half-yearly', 'yearly'],  // Bi-monthly can support bi-monthly, half-yearly, yearly (NOT quarterly)
      'quarterly': ['quarterly', 'half-yearly', 'yearly'],  // Quarterly can support quarterly, half-yearly, yearly
      'half-yearly': ['half-yearly', 'yearly'],  // Half-yearly can support half-yearly, yearly
      'yearly': ['yearly'],  // Yearly can only support yearly
      'custom': ['monthly', 'bi-monthly', 'quarterly', 'half-yearly', 'yearly', 'custom']
    };

    const canSupport = compatibilityMatrix[assignmentFreq]?.includes(normalizedFrequency) || false;

    if (!canSupport) {
      const supportedFrequencies = compatibilityMatrix[assignmentFreq] || [];
      let errorMessage = `Assignment frequency (${assignmentFreq}) cannot support requested frequency (${normalizedFrequency}). `;

      if (normalizedFrequency === 'quarterly' && assignmentFreq === 'bi-monthly') {
        errorMessage += `Bi-monthly assignments cannot support quarterly reporting because bi-monthly periods (Jan-Feb, Mar-Apr, etc.) do not align with quarterly periods (Q1, Q2, Q3, Q4).`;
      } else {
        errorMessage += `Assignment with ${assignmentFreq} frequency can only support: ${supportedFrequencies.join(', ')}. Requested: ${normalizedFrequency}.`;
      }

      return {
        isValid: false,
        incompatibleReason: errorMessage
      };
    }

    return {
      isValid: true
    };
  }

  /**
   * Validate basic input requirements before processing
   */
  private validateBasicInputRequirements(requestData: any): {isValid: boolean; message?: string} {
    const {
      main_data_source,
      sub_data_source,
      raw_parameters,
      indicator_parameters,
      filter_type,
      applied_common_filter,
      applied_specific_filter,
      type_of_data,
      type_of_format,
      table_config,
      chart_config
    } = requestData;

    // 1. Check required fields
    if (!main_data_source) {
      return {
        isValid: false,
        message: 'main_data_source is required'
      };
    }

    if (!type_of_data) {
      return {
        isValid: false,
        message: 'type_of_data is required and must be either "queried_data" or "direct_extract"'
      };
    }

    if (!type_of_format) {
      return {
        isValid: false,
        message: 'type_of_format is required and must be one of "tabular_form_data", "value_field", or "chart_data"'
      };
    }

    if (!filter_type) {
      return {
        isValid: false,
        message: 'filter_type is required and must be either "applied_common_filter" or "applied_specific_filter"'
      };
    }

    // 2. Conditional validation for main_data_source = quantitative
    if (main_data_source === 'quantitative') {
      if (!sub_data_source) {
        return {
          isValid: false,
          message: 'sub_data_source is required when main_data_source is "quantitative"'
        };
      }

      // Check raw_parameters when sub_data_source is raw
      if (sub_data_source === 'raw') {
        if (!raw_parameters) {
          return {
            isValid: false,
            message: 'raw_parameters is required when main_data_source is "quantitative" and sub_data_source is "raw"'
          };
        }
      }

      // Check indicator_parameters when sub_data_source is indicator
      if (sub_data_source === 'indicator') {
        if (!indicator_parameters) {
          return {
            isValid: false,
            message: 'indicator_parameters is required when main_data_source is "quantitative" and sub_data_source is "indicator"'
          };
        }
      }
    }

    // 3. Validate filter presence
    const activeFilterType = filter_type === 'applied_common_filter' ? applied_common_filter : filter_type === 'applied_specific_filter' ? applied_specific_filter : null;

    if (!activeFilterType) {
      return {
        isValid: false,
        message: `${filter_type} object is required but not provided`
      };
    }

    // 4. Validate filter content
    const filterValidation = this.validateFilterContent(activeFilterType, filter_type);
    if (!filterValidation.isValid) {
      return filterValidation;
    }

    // 5. Validate type_of_data specific requirements
    if (type_of_data === 'queried_data') {
      if (!requestData.query_details) {
        return {
          isValid: false,
          message: 'query_details is required when type_of_data is "queried_data"'
        };
      }

      const queryValidation = this.validateQueryDetails(requestData.query_details);
      if (!queryValidation.isValid) {
        return queryValidation;
      }
    }

    // 6. Validate type_of_format specific requirements
    if (type_of_format === 'tabular_form_data') {
      if (!table_config) {
        return {
          isValid: false,
          message: 'table_config is required when type_of_format is "tabular_form_data"'
        };
      }
    }

    if (type_of_format === 'chart_data') {
      if (!chart_config) {
        return {
          isValid: false,
          message: 'chart_config is required when type_of_format is "chart_data"'
        };
      }
    }

    return {
      isValid: true
    };
  }

  /**
   * Validate query_details requirements for queried_data type
   */
  private validateQueryDetails(query_details: any): {isValid: boolean; message?: string} {
    if (!query_details.query_type) {
      return {
        isValid: false,
        message: 'query_type is required in query_details'
      };
    }

    const validQueryTypes = ['sum', 'ratio', 'percentage', 'count'];
    if (!validQueryTypes.includes(query_details.query_type)) {
      return {
        isValid: false,
        message: `query_type must be one of: ${validQueryTypes.join(', ')}`
      };
    }

    if (!query_details.sub_query_type) {
      return {
        isValid: false,
        message: 'sub_query_type is required in query_details'
      };
    }

    const validSubQueryTypes = [
      'sum_for_entity_for_periods',
      'sum_for_all_entities_by_period',
      'breakdown_by_entity',
      'breakdown_by_period'
    ];
    if (!validSubQueryTypes.includes(query_details.sub_query_type)) {
      return {
        isValid: false,
        message: `sub_query_type must be one of: ${validSubQueryTypes.join(', ')}`
      };
    }

    // Validate based on query_type
    if (query_details.query_type === 'sum') {
      // For sum, query_parameters is not required
      if (query_details.query_parameters) {
        return this.validateSumQueryParameters(query_details.query_parameters);
      }
    } else if (query_details.query_type === 'ratio') {
      // For ratio, query_parameters is required
      if (!query_details.query_parameters) {
        return {
          isValid: false,
          message: 'query_parameters is required when query_type is "ratio"'
        };
      }
      return this.validatePercentageRatioQueryParameters(query_details.query_parameters);
    } else if (query_details.query_type === 'percentage') {
      // For percentage, query_parameters is not required for now
      if (query_details.query_parameters) {
        return this.validatePercentageRatioQueryParameters(query_details.query_parameters);
      }
    }

    // For now, only sum is implemented
    if (query_details.query_type !== 'sum') {
      return {
        isValid: false,
        message: `query_type "${query_details.query_type}" is not yet implemented. Currently only "sum" is supported.`
      };
    }

    return {
      isValid: true
    };
  }

  /**
   * Validate query parameters for sum query type
   */
  private validateSumQueryParameters(query_parameters: any): {isValid: boolean; message?: string} {
    if (!query_parameters.entity_selected) {
      return {
        isValid: false,
        message: 'entity_selected is required in query_parameters for sum query_type'
      };
    }

    if (!Array.isArray(query_parameters.entity_selected)) {
      return {
        isValid: false,
        message: 'entity_selected must be an array'
      };
    }

    if (!query_parameters.reporting_period) {
      return {
        isValid: false,
        message: 'reporting_period is required in query_parameters for sum query_type'
      };
    }

    // Check for either dcf_name or indicator_name
    const hasDcfName = query_parameters.dcf_name && Array.isArray(query_parameters.dcf_name) && query_parameters.dcf_name.length > 0;
    const hasIndicatorName = query_parameters.indicator_name && Array.isArray(query_parameters.indicator_name) && query_parameters.indicator_name.length > 0;

    if (!hasDcfName && !hasIndicatorName) {
      return {
        isValid: false,
        message: 'Either dcf_name or indicator_name array is required in query_parameters for sum query_type'
      };
    }

    if (hasDcfName && hasIndicatorName) {
      return {
        isValid: false,
        message: 'Only one of dcf_name or indicator_name should be provided, not both'
      };
    }

    return {
      isValid: true
    };
  }

  /**
   * Validate query parameters for percentage/ratio query types
   */
  private validatePercentageRatioQueryParameters(query_parameters: any): {isValid: boolean; message?: string} {
    if (!query_parameters.entity_selected) {
      return {
        isValid: false,
        message: 'entity_selected is required in query_parameters for percentage/ratio query_type'
      };
    }

    if (!query_parameters.reporting_period) {
      return {
        isValid: false,
        message: 'reporting_period is required in query_parameters for percentage/ratio query_type'
      };
    }

    if (!query_parameters.parameter_a) {
      return {
        isValid: false,
        message: 'parameter_a is required in query_parameters for percentage/ratio query_type'
      };
    }

    if (!query_parameters.parameter_b) {
      return {
        isValid: false,
        message: 'parameter_b is required in query_parameters for percentage/ratio query_type'
      };
    }

    return {
      isValid: true
    };
  }

  /**
   * Validate filter content requirements
   */
  private validateFilterContent(activeFilterType: any, filterTypeName: string): {isValid: boolean; message?: string} {
    if (!activeFilterType.year) {
      return {
        isValid: false,
        message: `year is required in ${filterTypeName}`
      };
    }

    if (!Array.isArray(activeFilterType.year)) {
      return {
        isValid: false,
        message: `year must be an array of numbers in ${filterTypeName}`
      };
    }

    if (activeFilterType.year.length === 0) {
      return {
        isValid: false,
        message: `year array cannot be empty in ${filterTypeName}`
      };
    }

    if (!activeFilterType.year.every((year: any) => typeof year === 'number' && !isNaN(year))) {
      return {
        isValid: false,
        message: `year array must contain only valid numbers in ${filterTypeName}`
      };
    }

    if (!activeFilterType.reporting_period) {
      return {
        isValid: false,
        message: `reporting_period is required in ${filterTypeName}`
      };
    }

    // Validate Custom period requirements
    if (activeFilterType.reporting_period === 'Custom') {
      if (!activeFilterType.reporting_period_from) {
        return {
          isValid: false,
          message: `reporting_period_from is required when reporting_period is "Custom" in ${filterTypeName}`
        };
      }
      if (!activeFilterType.reporting_period_to) {
        return {
          isValid: false,
          message: `reporting_period_to is required when reporting_period is "Custom" in ${filterTypeName}`
        };
      }
    }

    if (!activeFilterType.entity) {
      return {
        isValid: false,
        message: `entity is required in ${filterTypeName}`
      };
    }

    if (!Array.isArray(activeFilterType.entity)) {
      return {
        isValid: false,
        message: `entity must be an array of strings in ${filterTypeName}`
      };
    }

    if (activeFilterType.entity.length === 0) {
      return {
        isValid: false,
        message: `entity array cannot be empty in ${filterTypeName}`
      };
    }

    if (!activeFilterType.entity.every((entity: any) => typeof entity === 'string')) {
      return {
        isValid: false,
        message: `entity array must contain only strings in ${filterTypeName}`
      };
    }

    return {
      isValid: true
    };
  }

  /**
   * Validate year and entity requirements based on type_of_data
   * Year & entity values are taken from the filter_type object (applied_common_filter or applied_specific_filter)
   */
  private validateYearAndEntityForDataType(type_of_data: string, activeFilterType: any): {isValid: boolean; message?: string} {
    if (!activeFilterType.year || !activeFilterType.entity) {
      return {
        isValid: false,
        message: 'Both year and entity are required'
      };
    }

    // Year should always be an array now based on our schema
    if (!Array.isArray(activeFilterType.year)) {
      return {
        isValid: false,
        message: 'Year must be an array of numbers'
      };
    }

    if (!Array.isArray(activeFilterType.entity)) {
      return {
        isValid: false,
        message: 'Entity must be an array of strings'
      };
    }

    const yearLength = activeFilterType.year.length;
    const entityLength = activeFilterType.entity.length;

    // Get current format for better error messages
    const currentYearFormat = `array with ${yearLength} element(s)`;
    const currentEntityFormat = `array with ${entityLength} element(s)`;

    if (type_of_data === 'queried_data') {
      // For queried_data: year should be array of numbers, minimum length 1 for both
      // Only one can be multiple (not both)
      if (yearLength < 1) {
        return {
          isValid: false,
          message: 'Year array must have at least 1 element'
        };
      }

      if (entityLength < 1) {
        return {
          isValid: false,
          message: 'Entity array must have at least 1 element'
        };
      }

      // Check that only one can be multiple (not both)
      if (yearLength > 1 && entityLength > 1) {
        return {
          isValid: false,
          message: `For queried_data, only one of year or entity can have multiple values, not both. Current: year has ${yearLength} values, entity has ${entityLength} values.`
        };
      }

      // Validate that year array contains only numbers
      if (!activeFilterType.year.every((year: any) => typeof year === 'number' && !isNaN(year))) {
        return {
          isValid: false,
          message: 'Year array must contain only valid numbers'
        };
      }

    } else if (type_of_data === 'direct_extract') {
      // For direct_extract: both year and entity should have exactly length 1
      if (yearLength !== 1) {
        return {
          isValid: false,
          message: `For direct_extract, year array must have exactly 1 element. Current format: ${currentYearFormat}.`
        };
      }

      if (entityLength !== 1) {
        return {
          isValid: false,
          message: `For direct_extract, entity must have exactly 1 element. Current format: ${currentEntityFormat}.`
        };
      }

      // Validate year is a number
      const yearValue = activeFilterType.year[0];
      if (typeof yearValue !== 'number' || isNaN(yearValue)) {
        return {
          isValid: false,
          message: 'Year must be a valid number'
        };
      }
    }

    return {
      isValid: true
    };
  }

  @post('/stt-report/generate-report')
  @response(200, {
    description: 'STT Report Generation - Main entry function with complete input format',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            valid_periods: {type: 'array', items: {type: 'string'}},
            data_granularity: {type: 'string'}
          }
        }
      }
    },
  })
  async generateReport(@requestBody({
    content: {
      'application/json': {
        required: true,
        schema: {
          type: 'object',
          properties: {
            main_data_source: {
              type: 'string',
              enum: ['quantitative', 'qualitative', 'config'],
              description: 'Main data source type'
            },
            sub_data_source: {
              type: 'string',
              enum: ['raw', 'indicator'],
              description: 'Required if main_data_source is Quantitative'
            },
            raw_parameters: {
              type: 'object',
              description: 'Required if sub_data_source is raw',
              properties: {
                dcf_name: {
                  type: 'array',
                  items: {type: 'number'},
                  description: 'Single DCF name in array format'
                },
                sub_status: {
                  type: 'string',
                  enum: ['live', 'locked'],
                  description: 'live - other than draft, locked - only approved'
                },
                breakdown: {
                  type: 'boolean',
                  description: 'Enable/disable breakdown'
                },
                breakdown_data: {
                  type: 'string',
                  enum: ['dcf_name', 'data_point_name'],
                  description: 'Required if breakdown is true'
                }
              }
            },
            indicator_parameters: {
              type: 'object',
              description: 'Required if sub_data_source is indicator',
              properties: {
                indicator_name: {
                  type: 'array',
                  items: {type: 'number'},
                  description: 'Single indicator name in array format'
                },
                sub_status: {
                  type: 'string',
                  enum: ['locked', 'breakdown'],
                  description: 'Status for indicator data'
                },
                breakdown_data: {
                  type: 'array',
                  items: {type: 'number'},
                  description: 'Required if sub_status is breakdown'
                }
              }
            },
            filter_type: {
              type: 'string',
              enum: ['applied_common_filter', 'applied_specific_filter'],
              description: 'Type of filter to apply'
            },
            applied_common_filter: {
              type: 'object',
              description: 'Filters applied on the template by the user',
              properties: {
                year: {
                  type: 'array',
                  items: {type: 'number'},
                  minItems: 1,
                  description: 'Array of years - must have exactly 1 element for direct_extract, can have multiple for queried_data'
                },
                reporting_period: {
                  type: 'string',
                  enum: ['Monthly', 'Bi-Monthly', 'Quarterly', 'Half-Yearly', 'Yearly', 'Custom'],
                  description: 'Reporting period type'
                },
                reporting_period_from: {
                  type: 'string',
                  description: 'Start period (e.g., 2023-01)'
                },
                reporting_period_to: {
                  type: 'string',
                  description: 'End period (e.g., 2023-12)'
                },
                locked_date: {
                  type: 'string',
                  description: 'Locked date (e.g., 2024-01-15) or NA'
                },
                entity: {
                  type: 'array',
                  items: {type: 'string'},
                  minItems: 1,
                  description: 'Entity ID or Name'
                }
              }
            },
            applied_specific_filter: {
              type: 'object',
              description: 'Filters applied through the code',
              properties: {
                year: {
                  type: 'array',
                  items: {type: 'number'},
                  minItems: 1,
                  description: 'Array of years - must have exactly 1 element for direct_extract, can have multiple for queried_data'
                },
                reporting_period: {
                  type: 'string',
                  enum: ['Monthly', 'Bi-Monthly', 'Quarterly', 'Half-Yearly', 'Yearly', 'Custom'],
                  description: 'Reporting period type'
                },
                reporting_period_from: {
                  type: 'string',
                  description: 'Start period (e.g., 2023-01)'
                },
                reporting_period_to: {
                  type: 'string',
                  description: 'End period (e.g., 2023-12)'
                },
                locked_date: {
                  type: 'string',
                  description: 'Locked date (e.g., 2024-01-15) or NA'
                },
                entity: {
                  type: 'array',
                  items: {type: 'string'},
                  minItems: 1,
                  description: 'Entity ID or Name'
                }
              }
            },
            type_of_data: {
              type: 'string',
              enum: ['queried_data', 'direct_extract'],
              description: 'Data extraction type'
            },
            type_of_format: {
              type: 'string',
              enum: ['tabular_form_data', 'value_field', 'chart_data'],
              description: 'Output format type'
            },
            query_details: {
              type: 'object',
              description: 'Required only if type_of_data is queried_data',
              properties: {
                query_type: {
                  type: 'string',
                  enum: ['sum', 'ratio', 'percentage', 'count'],
                  description: 'Type of query operation'
                },
                sub_query_type: {
                  type: 'string',
                  description: 'Sub query type (e.g., sum for entity)'
                },
                query_parameters: {
                  type: 'object',
                  description: 'Query parameters: filter_type, entity selected, reporting period'
                }
              }
            },
            table_config: {
              type: 'object',
              description: 'Required only if type_of_format is tabular_form_data',
              properties: {
                period_breakdown: {
                  type: 'string',
                  description: 'Options: monthly, quarterly'
                },
                entity_breakdown: {
                  type: 'boolean',
                  description: 'Enable/disable entity breakdown'
                },
                entity_details: {
                  type: 'string',
                  description: 'Entity selected - required if entity_breakdown is true'
                },
                dcf_breakdown: {
                  type: 'boolean',
                  description: 'Enable/disable DCF breakdown'
                }
              }
            },
            chart_config: {
              type: 'object',
              description: 'Required only if type_of_format is chart_data',
              properties: {
                period_breakdown: {
                  type: 'string',
                  description: 'Options: monthly, quarterly'
                },
                entity_breakdown: {
                  type: 'boolean',
                  description: 'Enable/disable entity breakdown'
                },
                dcf_breakdown: {
                  type: 'boolean',
                  description: 'Enable/disable DCF breakdown'
                },
                entity_details: {
                  type: 'string',
                  description: 'Entity selected - required if entity_breakdown is true'
                }
              }
            }
          },
          required: ['main_data_source', 'filter_type', 'type_of_data', 'type_of_format']
        }
      }
    }
  })
  requestData: any): Promise<{status: boolean; message: string; valid_periods?: string[]; data_granularity?: string}> {
    try {
      const {
        main_data_source,
        sub_data_source,
        raw_parameters,
        indicator_parameters,
        filter_type,
        applied_common_filter,
        applied_specific_filter,
        type_of_data,
        type_of_format,
        table_config,
        chart_config
      } = requestData;

      // Basic input validation before proceeding
      const basicValidation = this.validateBasicInputRequirements(requestData);
      if (!basicValidation.isValid) {
        return {
          status: false,
          message: basicValidation.message || 'Basic input validation failed'
        };
      }

      const activeFilterType = filter_type === 'applied_common_filter' ? applied_common_filter : filter_type === 'applied_specific_filter' ? applied_specific_filter : null;

      // Validate type_of_data specific requirements
      const yearValidation = this.validateYearAndEntityForDataType(type_of_data, activeFilterType);
      if (!yearValidation.isValid) {
        return {
          status: false,
          message: yearValidation.message || 'Validation failed for year and entity requirements'
        };
      }

      // Validate year limit
      const maxAllowedYear = 2025;
      let requestedYear: number;

      if (activeFilterType.reporting_period === 'Custom') {
        requestedYear = new Date(activeFilterType.reporting_period_from).getFullYear();
      } else {
        // Handle year as either number or array
        if (Array.isArray(activeFilterType.year)) {
          // For array, check the maximum year
          requestedYear = Math.max(...activeFilterType.year);
        } else {
          requestedYear = parseInt(activeFilterType.year);
        }
      }

      if (requestedYear > maxAllowedYear) {
        return {
          status: false,
          message: `Cannot request data for year ${requestedYear}. Maximum allowed year is ${maxAllowedYear}.`
        };
      }

      // If year is array, validate all years
      if (Array.isArray(activeFilterType.year)) {
        const invalidYears = activeFilterType.year.filter((year: number) => year > maxAllowedYear);
        if (invalidYears.length > 0) {
          return {
            status: false,
            message: `Cannot request data for years ${invalidYears.join(', ')}. Maximum allowed year is ${maxAllowedYear}.`
          };
        }
      }

      if (activeFilterType.reporting_period === 'Custom') {
        const endYear = new Date(activeFilterType.reporting_period_to).getFullYear();
        if (endYear > maxAllowedYear) {
          return {
            status: false,
            message: `Custom period end year ${endYear} exceeds maximum allowed year ${maxAllowedYear}.`
          };
        }
      }

      // Handle query_details processing for queried_data
      if (type_of_data === 'queried_data') {
        return await this.processQueriedData(requestData, activeFilterType);
      }

      // Call the validation function first for direct_extract
      const validationResult = await this.validateInputParameters(
        main_data_source,
        sub_data_source,
        indicator_parameters,
        raw_parameters,
        activeFilterType
      );

      if (!validationResult.status) {
        return validationResult;
      }


      // const formConfiguration = await this.loadFormConfiguration(validationResult.dcfIds);

      // Call fetchDataFromDataLayer with validation result data
      const dataLayerResult = await this.fetchDataFromDataLayer(
        validationResult.dcfIds,
        validationResult.valid_periods,
        activeFilterType.entity,
        sub_data_source
      );

      // Calculate data completeness status for each period
      const totalExpectedCombinations = validationResult.dcfIds.length * activeFilterType.entity.length;
      const data_complete_status = validationResult.valid_periods.map((period: string) => {
        // Find the corresponding period data from fetchedData.period_data
        const periodData = dataLayerResult.fetchedData?.period_data?.find((p: any) => p.period === period);

        if (!periodData || periodData.data_count === 0) {
          return 0; // No data found
        } else if (periodData.data_count >= totalExpectedCombinations) {
          return 2; // Complete data (has data for all DCF-entity combinations)
        } else {
          return 1; // Partial data (has some data but not complete)
        }
      });

      // Extract only the data arrays from period_data
      const period_data = dataLayerResult.fetchedData?.period_data?.map((periodObj: any) => periodObj.data) || [];

      // Calculate data_sets - sum of computedValue for each period
      const data_sets = period_data.map((periodDataArray: any[]) => {
        if (periodDataArray.length === 0) {
          return 0; // No data = 0
        }

        // Sum the computedValue field from each data item
        return periodDataArray.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);
      });

      // Call type_of_format function to process the data based on format type
      const formatResult = await this.type_of_format(
        requestData.type_of_format,
        requestData.table_config,
        requestData.chart_config,
        data_sets,
        validationResult.valid_periods,
        activeFilterType.reporting_period,
        period_data,
        validationResult.dcfIds,
        activeFilterType.entity,
        sub_data_source,
        raw_parameters,
        indicator_parameters
      );

      // Return validation result with form configuration, period data, completeness status, data sets, and formatted result
      return {
        ...validationResult,
        period_data,
        data_complete_status,
        data_sets,
        ...formatResult
      };

    } catch (error) {
      return {
        status: false,
        message: `Error in report generation: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Process queried_data type requests with query_details
   */
  private async processQueriedData(requestData: any, activeFilterType: any): Promise<any> {
    try {
      const {query_details, main_data_source, sub_data_source} = requestData;

      // For now, only handle sum query_type
      if (query_details.query_type !== 'sum') {
        return {
          status: false,
          message: `query_type "${query_details.query_type}" is not yet implemented. Currently only "sum" is supported.`
        };
      }

      // Process sum query
      return await this.processSumQuery(query_details, activeFilterType, main_data_source, sub_data_source, requestData);

    } catch (error) {
      return {
        status: false,
        message: `Error processing queried data: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Process sum query type with different sub_query_types
   */
  private async processSumQuery(query_details: any, activeFilterType: any, main_data_source: string, sub_data_source: string, requestData?: any): Promise<any> {
    try {
      const {sub_query_type, query_parameters} = query_details;

      // For sum query_type, query_parameters is optional
      // If not provided, we can use data from raw_parameters/indicator_parameters and activeFilterType
      let effectiveQueryParams = query_parameters;

      if (!query_parameters) {
        // Build query_parameters from existing request data
        effectiveQueryParams = {
          entity_selected: activeFilterType.entity,
          reporting_period: activeFilterType.reporting_period
        };

        // Get dcf_name or indicator_name from raw_parameters or indicator_parameters
        if (sub_data_source === 'raw' && requestData?.raw_parameters?.dcf_name) {
          effectiveQueryParams.dcf_name = requestData.raw_parameters.dcf_name;
        } else if (sub_data_source === 'indicator' && requestData?.indicator_parameters?.indicator_name) {
          effectiveQueryParams.indicator_name = requestData.indicator_parameters.indicator_name;
        } else {
          return {
            status: false,
            message: 'Unable to determine DCF names or indicator names from request data. Please provide query_parameters or ensure raw_parameters/indicator_parameters contain the required data.'
          };
        }
      }

      // Get DCF IDs based on effective query parameters
      let dcfIds: number[] = [];

      if (effectiveQueryParams.dcf_name && effectiveQueryParams.dcf_name.length > 0) {
        // For DCF names, we need to resolve them to IDs
        // For now, assuming dcf_name contains DCF IDs directly
        dcfIds = effectiveQueryParams.dcf_name.map((id: any) => parseInt(id));
      } else if (effectiveQueryParams.indicator_name && effectiveQueryParams.indicator_name.length > 0) {
        // For indicator names, get associated DCF IDs
        const indicatorList = await this.userProfileController.getAssignedIndicator(94, {
          indicatorId: effectiveQueryParams.indicator_name
        });
        dcfIds = Array.from(new Set(indicatorList.flatMap((x: any) => x.dcfIds || []))) as number[];
      }

      if (dcfIds.length === 0) {
        return {
          status: false,
          message: 'No valid DCF IDs found for the specified query parameters'
        };
      }

      // Use entity_selected from effective query parameters
      const entities = effectiveQueryParams.entity_selected;

      // Get data from data layer
      const dataLayerResult = await this.fetchDataFromDataLayer(
        dcfIds,
        [], // We'll calculate periods based on query_parameters.reporting_period
        entities,
        sub_data_source
      );

      // Process based on sub_query_type
      switch (sub_query_type) {
        case 'sum_for_entity_for_periods':
          return this.processSumForEntityForPeriods(dataLayerResult, entities, effectiveQueryParams);

        case 'sum_for_all_entities_by_period':
          return this.processSumForAllEntitiesByPeriod(dataLayerResult, entities, effectiveQueryParams);

        case 'breakdown_by_entity':
          return this.processBreakdownByEntity(dataLayerResult, entities, effectiveQueryParams);

        case 'breakdown_by_period':
          return this.processBreakdownByPeriod(dataLayerResult, entities, effectiveQueryParams);

        default:
          return {
            status: false,
            message: `sub_query_type "${sub_query_type}" is not yet implemented`
          };
      }

    } catch (error) {
      return {
        status: false,
        message: `Error processing sum query: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Extract entity name from data item
   */
  private extractEntityName(dataItem: any): string {
    // Try different possible entity name fields
    return dataItem.entityName || dataItem.entity_name || dataItem.locationName ||
      dataItem.location_name || dataItem.name || `${dataItem.level}-${dataItem.locationId}`;
  }

  /**
   * Check if data item matches entity format (name or ID format)
   */
  private matchesEntityFormat(dataItem: any, entityName: string): boolean {
    // Check if entityName matches the level-locationId format
    if (entityName.includes('-')) {
      const [level, locationId] = entityName.split('-');
      return dataItem.level?.toString() === level && dataItem.locationId?.toString() === locationId;
    }

    // Check various entity name fields
    const itemEntityName = this.extractEntityName(dataItem);
    return itemEntityName === entityName;
  }

  /**
   * Process sum_for_entity_for_periods sub_query_type
   * Sum all values for the specified entity and selected periods (year specifically)
   */
  private processSumForEntityForPeriods(dataLayerResult: any, entities: string[], query_parameters: any): any {
    const result: any = {};

    // Process each entity separately
    entities.forEach(entityName => {
      let entityTotalSum = 0;
      const periodBreakdown: any = {};

      // Process period data if available
      if (dataLayerResult.fetchedData?.period_data) {
        dataLayerResult.fetchedData.period_data.forEach((periodObj: any, periodIndex: number) => {
          const periodData = periodObj.data || periodObj; // Handle both formats
          let periodSum = 0;

          // Filter data for this specific entity and sum values
          periodData.forEach((dataItem: any) => {
            // Match entity by name or ID format
            const itemEntityName = this.extractEntityName(dataItem);
            if (itemEntityName === entityName || this.matchesEntityFormat(dataItem, entityName)) {
              const value = parseFloat(dataItem.computedValue) || parseFloat(dataItem.value) || 0;
              periodSum += value;
            }
          });

          // Store period breakdown
          const periodLabel = periodObj.period || `Period_${periodIndex + 1}`;
          periodBreakdown[periodLabel] = periodSum;
          entityTotalSum += periodSum;
        });
      }

      result[entityName] = {
        total_sum: entityTotalSum,
        period_breakdown: periodBreakdown
      };
    });

    return {
      status: true,
      message: `Sum for entity for periods processed successfully for ${entities.length} entities`,
      query_type: 'sum',
      sub_query_type: 'sum_for_entity_for_periods',
      result
    };
  }

  /**
   * Process sum_for_all_entities_by_period sub_query_type
   * Fetch data for all selected entities for the given period, then compute the total sum
   */
  private processSumForAllEntitiesByPeriod(dataLayerResult: any, entities: string[], query_parameters: any): any {
    const result: any = {
      period_totals: {},
      grand_total: 0
    };

    // Process each period separately
    if (dataLayerResult.fetchedData?.period_data) {
      dataLayerResult.fetchedData.period_data.forEach((periodObj: any, periodIndex: number) => {
        const periodData = periodObj.data || periodObj;
        let periodTotal = 0;

        // Sum all values for all selected entities in this period
        periodData.forEach((dataItem: any) => {
          const itemEntityName = this.extractEntityName(dataItem);

          // Check if this data item belongs to any of the selected entities
          const belongsToSelectedEntity = entities.some(entityName =>
            itemEntityName === entityName || this.matchesEntityFormat(dataItem, entityName)
          );

          if (belongsToSelectedEntity) {
            const value = parseFloat(dataItem.computedValue) || parseFloat(dataItem.value) || 0;
            periodTotal += value;
          }
        });

        const periodLabel = periodObj.period || `Period_${periodIndex + 1}`;
        result.period_totals[periodLabel] = periodTotal;
        result.grand_total += periodTotal;
      });
    }

    return {
      status: true,
      message: `Sum for all entities by period processed successfully for ${entities.length} entities`,
      query_type: 'sum',
      sub_query_type: 'sum_for_all_entities_by_period',
      result
    };
  }

  /**
   * Process breakdown_by_entity sub_query_type
   * Repeat the sum logic for each entity separately, and return a breakdown (e.g., row-wise table)
   */
  private processBreakdownByEntity(dataLayerResult: any, entities: string[], query_parameters: any): any {
    const result: any = {
      entity_breakdown: {},
      summary: {
        total_entities: entities.length,
        grand_total: 0
      }
    };

    // Process each entity separately
    entities.forEach(entityName => {
      let entityTotal = 0;
      const entityDetails: any[] = [];
      const periodTotals: any = {};

      // Process period data for this entity
      if (dataLayerResult.fetchedData?.period_data) {
        dataLayerResult.fetchedData.period_data.forEach((periodObj: any, periodIndex: number) => {
          const periodData = periodObj.data || periodObj;
          let periodSum = 0;
          const periodDetails: any[] = [];

          // Filter and sum data for this specific entity
          periodData.forEach((dataItem: any) => {
            const itemEntityName = this.extractEntityName(dataItem);
            if (itemEntityName === entityName || this.matchesEntityFormat(dataItem, entityName)) {
              const value = parseFloat(dataItem.computedValue) || parseFloat(dataItem.value) || 0;
              periodSum += value;

              // Store individual data point details
              periodDetails.push({
                dcf_id: dataItem.dcfId,
                value: value,
                period: periodObj.period || `Period_${periodIndex + 1}`,
                data_point: dataItem.dataPoint || dataItem.indicator || 'Unknown'
              });
            }
          });

          const periodLabel = periodObj.period || `Period_${periodIndex + 1}`;
          periodTotals[periodLabel] = periodSum;
          entityTotal += periodSum;

          if (periodDetails.length > 0) {
            entityDetails.push({
              period: periodLabel,
              period_total: periodSum,
              data_points: periodDetails
            });
          }
        });
      }

      result.entity_breakdown[entityName] = {
        total: entityTotal,
        period_totals: periodTotals,
        details: entityDetails
      };

      result.summary.grand_total += entityTotal;
    });

    return {
      status: true,
      message: `Breakdown by entity processed successfully for ${entities.length} entities`,
      query_type: 'sum',
      sub_query_type: 'breakdown_by_entity',
      result
    };
  }

  /**
   * Process breakdown_by_period sub_query_type
   * Repeat the sum logic across each time unit in the period (e.g., Jan–Dec)
   */
  private processBreakdownByPeriod(dataLayerResult: any, entities: string[], query_parameters: any): any {
    const result: any = {
      period_breakdown: {},
      summary: {
        total_periods: 0,
        grand_total: 0
      }
    };

    // Process each period separately
    if (dataLayerResult.fetchedData?.period_data) {
      dataLayerResult.fetchedData.period_data.forEach((periodObj: any, periodIndex: number) => {
        const periodData = periodObj.data || periodObj;
        const periodLabel = periodObj.period || `Period_${periodIndex + 1}`;

        let periodTotal = 0;
        const entityBreakdown: any = {};

        // Process each entity within this period
        entities.forEach(entityName => {
          let entitySum = 0;
          const entityDataPoints: any[] = [];

          // Filter and sum data for this entity in this period
          periodData.forEach((dataItem: any) => {
            const itemEntityName = this.extractEntityName(dataItem);
            if (itemEntityName === entityName || this.matchesEntityFormat(dataItem, entityName)) {
              const value = parseFloat(dataItem.computedValue) || parseFloat(dataItem.value) || 0;
              entitySum += value;

              entityDataPoints.push({
                dcf_id: dataItem.dcfId,
                value: value,
                data_point: dataItem.dataPoint || dataItem.indicator || 'Unknown'
              });
            }
          });

          if (entitySum > 0 || entityDataPoints.length > 0) {
            entityBreakdown[entityName] = {
              total: entitySum,
              data_points: entityDataPoints
            };
          }

          periodTotal += entitySum;
        });

        // Store period breakdown
        result.period_breakdown[periodLabel] = {
          period_total: periodTotal,
          entity_breakdown: entityBreakdown,
          entities_count: Object.keys(entityBreakdown).length
        };

        result.summary.grand_total += periodTotal;
        result.summary.total_periods++;
      });
    }

    return {
      status: true,
      message: `Breakdown by period processed successfully for ${result.summary.total_periods} periods`,
      query_type: 'sum',
      sub_query_type: 'breakdown_by_period',
      result
    };
  }

  /**
   * Validate input parameters for STT report generation
   * This function performs all validation logic and returns the validation result
   */
  private async validateInputParameters(
    main_data_source: string,
    sub_data_source: string,
    indicator_parameters: any,
    raw_parameters: any,
    activeFilterType: any
  ): Promise<any> {
    try {
      // Process assignments if quantitative data source
      if (main_data_source === 'quantitative') {
        let dcfIds: number[] = [];

        if (sub_data_source === 'raw') {
          dcfIds = raw_parameters?.dcf_name || [];
        } else if (sub_data_source === 'indicator') {
          if (!indicator_parameters) {
            return {
              status: false,
              message: 'Indicator parameters are required for indicator data source',
              valid_periods: [],
              data_granularity: null,
              valid_assignment: [],
              dcfIds
            };
          }

          const indicatorList = await this.userProfileController.getAssignedIndicator(94, {
            indicatorId: indicator_parameters?.indicator_name || []
          });
          const dcfId_list = Array.from(new Set(indicatorList.flatMap((x: any) => x.dcfIds || []))) as number[];
          dcfIds = indicator_parameters?.breakdown ?
            dcfId_list.filter((x: number) => indicator_parameters?.breakdown_data?.includes(x)) :
            dcfId_list;
          console.log(dcfIds);
        }

        if (dcfIds.length === 0) {
          return {
            status: false,
            message: 'No valid DCF IDs found for processing',
            valid_periods: [],
            data_granularity: null,
            valid_assignment: [], dcfIds
          };
        }

        // Get assignments
        const dcfAssignment = await this.userProfileController.getAssignedIndicatorList(94, {
          where: {
            dcfId: {inq: dcfIds},
            locationId: {inq: activeFilterType.entity.map((entity: any) => entity.split('-')[1]).map((id: any) => parseInt(id)) || []}
          }
        }) as DcfAssignment[];

        if (!dcfAssignment || dcfAssignment.length === 0) {
          return {
            status: false,
            message: 'No valid assignments found for the given input',
            valid_periods: [],
            data_granularity: null,
            valid_assignment: [], dcfIds
          };
        }

        // Filter assignments based on requested period
        let periodStart: Date;
        let periodEnd: Date;

        if (activeFilterType.reporting_period === 'Custom') {
          periodStart = new Date(activeFilterType.reporting_period_from);
          periodEnd = new Date(activeFilterType.reporting_period_to);
        } else {
          // Handle year as either number or array
          let yearValue: number;
          if (Array.isArray(activeFilterType.year)) {
            // For array, use the minimum year for start and maximum year for end
            const minYear = Math.min(...activeFilterType.year);
            const maxYear = Math.max(...activeFilterType.year);
            periodStart = new Date(minYear.toString() + '-01-01');
            periodEnd = new Date(maxYear.toString() + '-12-31');
          } else {
            yearValue = activeFilterType.year;
            periodStart = new Date(yearValue.toString() + '-01-01');
            periodEnd = new Date(yearValue.toString() + '-12-31');
          }
        }

        // Set times to start and end of day for accurate comparison
        periodStart.setUTCHours(0, 0, 0, 0);
        periodEnd.setUTCHours(23, 59, 59, 999);

        const validPeriodAssignments = dcfAssignment.filter(assignment => {
          const assignmentStart = new Date(assignment.start_date);
          const assignmentEnd = assignment.end_date ? new Date(assignment.end_date) : new Date();

          assignmentStart.setUTCHours(0, 0, 0, 0);
          assignmentEnd.setUTCHours(23, 59, 59, 999);

          return assignmentStart <= periodEnd && assignmentEnd >= periodStart;
        });

        if (validPeriodAssignments.length === 0) {
          return {
            status: false,
            message: 'No valid assignments found for the requested period',
            valid_periods: [], periodStart, periodEnd,
            data_granularity: null,
            valid_assignment: [], dcfIds
          };
        }

        // Create a map to track assignments by entity and DCF
        const assignmentMap = new Map<string, any>();
        const frequencyMapping: {[key: number]: string} = {
          1: 'monthly',
          2: 'bi-monthly',
          3: 'quarterly',
          4: 'yearly',
          5: 'half-yearly'
        };

        // Group assignments by entity-dcf combination and validate frequency
        const validAssignments: any[] = [];
        const invalidAssignments: any[] = [];

        for (const assignment of validPeriodAssignments) {
          const entity = `${assignment.level}-${assignment.locationId}`;
          const key = `${assignment.dcfId}-${entity}`;

          // Check frequency compatibility using resolveReportingFrequency
          const frequencyResult = this.resolveReportingFrequency(
            {
              reporting_frequency: this.getFrequencyFromCode(assignment.frequency),
              reporting_period_from: activeFilterType.reporting_period_from,
              reporting_period: activeFilterType.reporting_period,
              reporting_period_to: activeFilterType.reporting_period_to,
              year: activeFilterType.year, entity: activeFilterType.entity
            },
            assignment
          );

          const assignmentResult: any = {
            dcfId: assignment.dcfId,
            entity,
            locationId: assignment.locationId,
            combination: key,
            assignment,
            frequencyResult,
            isValid: false,
            errors: [],
            assignmentDetails: {
              dcfId: assignment.dcfId,
              locationId: assignment.locationId,
              frequency: assignment.frequency,
              frequencyName: frequencyMapping[assignment.frequency],
              start_date: assignment.start_date,
              end_date: assignment.end_date,
              level: assignment.level
            }
          };

          if (!frequencyResult.isValid) {
            assignmentResult.isValid = false;
            assignmentResult.errors = [`Frequency incompatibility: ${frequencyResult.error}`];
            invalidAssignments.push(assignmentResult);
          } else {
            assignmentResult.isValid = true;
            assignmentResult.errors = [];
            assignmentResult.valid_periods = frequencyResult.periods?.valid_periods || [];
            assignmentResult.data_granularity = frequencyResult.periods?.data_granularity;
            assignmentResult.reportingPeriods = frequencyResult.periods;
            validAssignments.push(assignmentResult);
            assignmentMap.set(key, assignmentResult);
          }
        }

        // Check if we have any invalid assignments
        if (invalidAssignments.length > 0) {
          const errorMessages = invalidAssignments.map(invalid =>
            `DCF: ${invalid.dcfId}, Entity: ${invalid.entity} - ${invalid.errors[0]}`
          ).join('\n');

          return {
            status: false,
            message: `[VALIDATION ERROR] ${invalidAssignments.length} out of ${invalidAssignments.length + validAssignments.length} assignments have frequency incompatibility:\n${errorMessages}`,
            valid_periods: [], periodStart, periodEnd,
            data_granularity: null,
            valid_assignment: validAssignments,
            dcfIds
          };
        }

        // All assignments are valid - prepare response
        const validAssignmentsArray = Array.from(assignmentMap.values());

        // Transform validAssignments to include individual valid_periods for each assignment
        const validAssignmentsWithPeriods = validAssignmentsArray.map(assignment => ({
          ...assignment,
          valid_periods: assignment.reportingPeriods?.valid_periods || [],
          data_granularity: assignment.reportingPeriods?.data_granularity
        }));

        // Get valid_periods and data_granularity from the first assignment
        const valid_periods = validAssignmentsArray[0]?.reportingPeriods?.valid_periods || [];
        const data_granularity = validAssignmentsArray[0]?.reportingPeriods?.data_granularity || 'monthly';

        // Create frequency summary for the message
        const frequencies = validAssignments.map(v => v.assignment.frequency);
        const uniqueFrequencies = [...new Set(frequencies)];
        const frequencySummary = uniqueFrequencies.length === 1
          ? `All assignments have ${frequencyMapping[uniqueFrequencies[0]]} frequency.`
          : `Mixed frequencies: ${uniqueFrequencies.map(f => frequencyMapping[f]).join(', ')}. All can support ${activeFilterType.reporting_period} reporting.`;

        return {
          status: true,
          message: `All ${dcfIds.length * activeFilterType.entity.length} DCF-entity combinations have valid assignments. ${frequencySummary}`,
          valid_periods, dcfIds, periodStart, periodEnd,
          data_granularity,
          valid_assignment: validAssignmentsWithPeriods
        };
      }

      // If not quantitative, just return success for now
      return {
        status: true,
        message: 'Successfully generated periods',
        valid_periods: [],
        data_granularity: 'monthly',
        valid_assignment: []
      };

    } catch (error) {
      return {
        status: false,
        message: `Error validating input parameters: ${error instanceof Error ? error.message : 'Unknown error'}`,
        valid_periods: [],
        data_granularity: null,
        valid_assignment: []
      };
    }
  }

  /**
   * Load form configuration for the given DCF IDs
   * Fetches DCF details from formCollectionRepository and returns mapped structure
   */
  private async loadFormConfiguration(dcfIds: number[]): Promise<any> {
    try {
      // Get DCF forms from formCollectionRepository
      const dcfForms = await this.formCollectionRepository.find({
        where: {
          id: {inq: dcfIds}
        },
        fields: ['id', 'title']
      });

      // Map to the required structure
      const mapped_dcf_forms = dcfForms.map(form => ({
        dcf_name: form.title,
        data_points: [] // Keep empty array for now as requested
      }));

      return {
        mapped_dcf_forms
      };

    } catch (error) {
      console.error('Error loading form configuration:', error);
      return {
        mapped_dcf_forms: []
      };
    }
  }

  /**
   * Fetch data from data layer based on DCF IDs, valid periods, entity, and data source
   */
  private async fetchDataFromDataLayer(
    _dcfIds: number[],
    valid_periods: string[],
    entity: string[],
    _sub_data_source: string
  ): Promise<any> {
    try {
      // Calculate startMonth and endMonth from valid_periods
      let startMonth: string;
      let endMonth: string;

      if (valid_periods.length > 0) {
        // Determine period format and extract start/end months
        const firstPeriod = valid_periods[0];
        const lastPeriod = valid_periods[valid_periods.length - 1];

        if (firstPeriod.includes('-') && firstPeriod.match(/^\d{4}-\d{2}$/)) {
          // Monthly format: "2025-01"
          const [startYear, startMonthNum] = firstPeriod.split('-');
          const [endYear, endMonthNum] = lastPeriod.split('-');
          startMonth = `${this.getMonthName(parseInt(startMonthNum))}-${startYear}`;
          endMonth = `${this.getMonthName(parseInt(endMonthNum))}-${endYear}`;
        } else if (firstPeriod.includes('Q')) {
          // Quarterly format: "Q1-2025"
          const [startQ, startYear] = firstPeriod.split('-');
          const [endQ, endYear] = lastPeriod.split('-');
          startMonth = `${this.getQuarterStartMonth(startQ)}-${startYear}`;
          endMonth = `${this.getQuarterEndMonth(endQ)}-${endYear}`;
        } else if (firstPeriod.includes('H')) {
          // Half-yearly format: "H1-2025"
          const [startH, startYear] = firstPeriod.split('-');
          const [endH, endYear] = lastPeriod.split('-');
          startMonth = `${this.getHalfYearStartMonth(startH)}-${startYear}`;
          endMonth = `${this.getHalfYearEndMonth(endH)}-${endYear}`;
        } else if (firstPeriod.includes('BM')) {
          // Bi-monthly format: "BM1-2025"
          const [startBM, startYear] = firstPeriod.split('-');
          const [endBM, endYear] = lastPeriod.split('-');
          startMonth = `${this.getBiMonthlyStartMonth(startBM)}-${startYear}`;
          endMonth = `${this.getBiMonthlyEndMonth(endBM)}-${endYear}`;
        } else {
          // Yearly format: "2025"
          startMonth = `Jan-${firstPeriod}`;
          endMonth = `Dec-${lastPeriod}`;
        }
      } else {
        // Fallback to current year
        const currentYear = new Date().getFullYear();
        startMonth = `Jan-${currentYear}`;
        endMonth = `Dec-${currentYear}`;
      }

      // Get indicator data approval indicators
      const dataPoints = await this.userProfileController.getIndicatorDataApprovalIndicators(94, {
        year: {startMonth, endMonth}
      });

      // Filter by entity
      const entityFilteredDataPoints = dataPoints.filter((x: any) =>
        entity?.includes(x.level + "-" + x.locationId) && _dcfIds.includes(x.dcfId)
      );

      // Determine data granularity from valid_periods format
      let dataGranularity: string;
      if (valid_periods.length > 0) {
        const firstPeriod = valid_periods[0];
        if (firstPeriod.includes('Q')) {
          dataGranularity = 'quarterly';
        } else if (firstPeriod.includes('H')) {
          dataGranularity = 'half-yearly';
        } else if (firstPeriod.includes('BM')) {
          dataGranularity = 'bi-monthly';
        } else if (firstPeriod.match(/^\d{4}$/)) {
          dataGranularity = 'yearly';
        } else {
          dataGranularity = 'monthly';
        }
      } else {
        dataGranularity = 'monthly';
      }

      // Call appropriate fetch function based on data granularity
      let fetchedData: any;
      switch (dataGranularity) {
        case 'monthly':
          fetchedData = await this.fetchMonthlyDcfData(entityFilteredDataPoints, valid_periods);
          break;
        case 'bi-monthly':
          fetchedData = await this.fetchBiMonthlyDcfData(entityFilteredDataPoints, valid_periods);
          break;
        case 'quarterly':
          fetchedData = await this.fetchQuarterlyDcfData(entityFilteredDataPoints, valid_periods);
          break;
        case 'half-yearly':
          fetchedData = await this.fetchHalfYearlyDcfData(entityFilteredDataPoints, valid_periods);
          break;
        case 'yearly':
          fetchedData = await this.fetchYearlyDcfData(entityFilteredDataPoints, valid_periods);
          break;
        default:
          fetchedData = await this.fetchMonthlyDcfData(entityFilteredDataPoints, valid_periods);
      }

      return {
        dataPoints: entityFilteredDataPoints,
        fetchedData, act: dataPoints,
        dataGranularity,
        period: {
          startMonth,
          endMonth
        }
      };

    } catch (error) {
      console.error('Error fetching data from data layer:', error);
      return {
        dataPoints: [],
        fetchedData: null,
        dataGranularity: 'monthly',
        period: {
          startMonth: '',
          endMonth: ''
        }
      };
    }
  }

  // Helper methods for period conversion
  private getMonthName(monthNum: number): string {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months[monthNum - 1] || 'Jan';
  }

  private getQuarterStartMonth(quarter: string): string {
    switch (quarter) {
      case 'Q1': return 'Jan';
      case 'Q2': return 'Apr';
      case 'Q3': return 'Jul';
      case 'Q4': return 'Oct';
      default: return 'Jan';
    }
  }

  private getQuarterEndMonth(quarter: string): string {
    switch (quarter) {
      case 'Q1': return 'Mar';
      case 'Q2': return 'Jun';
      case 'Q3': return 'Sep';
      case 'Q4': return 'Dec';
      default: return 'Mar';
    }
  }

  private getHalfYearStartMonth(half: string): string {
    switch (half) {
      case 'H1': return 'Jan';
      case 'H2': return 'Jul';
      default: return 'Jan';
    }
  }

  private getHalfYearEndMonth(half: string): string {
    switch (half) {
      case 'H1': return 'Jun';
      case 'H2': return 'Dec';
      default: return 'Jun';
    }
  }

  private getBiMonthlyStartMonth(biMonth: string): string {
    switch (biMonth) {
      case 'BM1': return 'Jan';
      case 'BM2': return 'Mar';
      case 'BM3': return 'May';
      case 'BM4': return 'Jul';
      case 'BM5': return 'Sep';
      case 'BM6': return 'Nov';
      default: return 'Jan';
    }
  }

  private getBiMonthlyEndMonth(biMonth: string): string {
    switch (biMonth) {
      case 'BM1': return 'Feb';
      case 'BM2': return 'Apr';
      case 'BM3': return 'Jun';
      case 'BM4': return 'Aug';
      case 'BM5': return 'Oct';
      case 'BM6': return 'Dec';
      default: return 'Feb';
    }
  }

  private getBiMonthlyRange(biMonthNum: number): {start: number; end: number} {
    const ranges = {
      1: {start: 1, end: 2},   // BM1: Jan-Feb
      2: {start: 3, end: 4},   // BM2: Mar-Apr
      3: {start: 5, end: 6},   // BM3: May-Jun
      4: {start: 7, end: 8},   // BM4: Jul-Aug
      5: {start: 9, end: 10},  // BM5: Sep-Oct
      6: {start: 11, end: 12}  // BM6: Nov-Dec
    };
    return ranges[biMonthNum as keyof typeof ranges] || {start: 1, end: 2};
  }

  // Data fetching methods that split data by valid periods
  // Note: valid_periods can contain any subset of possible periods (partial or complete)
  // Examples: ["2025-01"] or ["2025-01", "2025-03", "2025-05"] or ["2025-01", "2025-02", ..., "2025-12"]
  private async fetchMonthlyDcfData(entityFilteredDataPoints: any[], valid_periods: string[]): Promise<any> {
    console.log('Fetching monthly DCF data for', entityFilteredDataPoints.length, 'data points');
    console.log('Processing periods:', valid_periods);

    const periodData = valid_periods.map(period => {
      // Extract year and month from period (e.g., "2025-01")
      const [year, month] = period.split('-');
      const monthNum = parseInt(month);

      // Filter data points that fall within this month using rp property
      const periodDataPoints = entityFilteredDataPoints.filter((dataPoint: any) => {
        if (!dataPoint.rp || !Array.isArray(dataPoint.rp)) return false;

        // Check if the period exists in the rp array
        const targetPeriod = `${month}-${year}`; // Convert to MM-YYYY format
        return dataPoint.rp.includes(targetPeriod);
      });

      return {
        period,
        period_label: `${this.getMonthName(monthNum)} ${year}`,
        data_count: periodDataPoints.length,
        data: periodDataPoints
      };
    });

    return {
      type: 'monthly',
      total_periods: valid_periods.length,
      period_data: periodData,
      total_data_points: entityFilteredDataPoints.length
    };
  }

  // Examples: ["BM1-2025"] or ["BM1-2025", "BM3-2025"] or ["BM1-2025", "BM2-2025", ..., "BM6-2025"]
  private async fetchBiMonthlyDcfData(entityFilteredDataPoints: any[], valid_periods: string[]): Promise<any> {
    console.log('Fetching bi-monthly DCF data for', entityFilteredDataPoints.length, 'data points');
    console.log('Processing periods:', valid_periods);

    const periodData = valid_periods.map(period => {
      // Extract bi-month and year from period (e.g., "BM1-2025")
      const [biMonth, year] = period.split('-');
      const biMonthNum = parseInt(biMonth.replace('BM', ''));

      // Define bi-monthly month ranges
      const biMonthRanges = {
        1: {start: 1, end: 2},   // BM1: Jan-Feb
        2: {start: 3, end: 4},   // BM2: Mar-Apr
        3: {start: 5, end: 6},   // BM3: May-Jun
        4: {start: 7, end: 8},   // BM4: Jul-Aug
        5: {start: 9, end: 10},  // BM5: Sep-Oct
        6: {start: 11, end: 12}  // BM6: Nov-Dec
      };

      const range = biMonthRanges[biMonthNum as keyof typeof biMonthRanges];

      // Filter data points that fall within this bi-monthly period using rp property
      const periodDataPoints = entityFilteredDataPoints.filter((dataPoint: any) => {
        if (!dataPoint.rp || !Array.isArray(dataPoint.rp)) return false;

        // Generate all months in this bi-monthly period
        const biMonthlyMonths: string[] = [];
        for (let month = range.start; month <= range.end; month++) {
          biMonthlyMonths.push(`${month.toString().padStart(2, '0')}-${year}`);
        }

        // Check if rp contains months from this bi-monthly period
        const rpMonthsInPeriod = dataPoint.rp.filter((rpMonth: string) => biMonthlyMonths.includes(rpMonth));

        // Must have at least one month from this period
        if (rpMonthsInPeriod.length === 0) return false;

        // Check if rp spans multiple bi-monthly periods (invalid)
        const biMonthlyPeriodsSpanned = new Set<number>();
        for (const rpMonth of dataPoint.rp) {
          const [monthStr, yearStr] = rpMonth.split('-');
          if (yearStr !== year) continue; // Different year, ignore

          const monthNum = parseInt(monthStr);
          // Find which bi-monthly period this month belongs to
          if (monthNum >= 1 && monthNum <= 2) biMonthlyPeriodsSpanned.add(1);
          else if (monthNum >= 3 && monthNum <= 4) biMonthlyPeriodsSpanned.add(2);
          else if (monthNum >= 5 && monthNum <= 6) biMonthlyPeriodsSpanned.add(3);
          else if (monthNum >= 7 && monthNum <= 8) biMonthlyPeriodsSpanned.add(4);
          else if (monthNum >= 9 && monthNum <= 10) biMonthlyPeriodsSpanned.add(5);
          else if (monthNum >= 11 && monthNum <= 12) biMonthlyPeriodsSpanned.add(6);
        }

        // Valid if rp doesn't span multiple bi-monthly periods
        return biMonthlyPeriodsSpanned.size <= 1;
      });

      return {
        period,
        period_label: `${biMonth} ${year} (${this.getMonthName(range.start)}-${this.getMonthName(range.end)})`,
        data_count: periodDataPoints.length,
        data: periodDataPoints
      };
    });

    return {
      type: 'bi-monthly',
      total_periods: valid_periods.length,
      period_data: periodData,
      total_data_points: entityFilteredDataPoints.length
    };
  }

  // Examples: ["Q1-2025"] or ["Q1-2025", "Q3-2025"] or ["Q1-2025", "Q2-2025", "Q3-2025", "Q4-2025"]
  private async fetchQuarterlyDcfData(entityFilteredDataPoints: any[], valid_periods: string[]): Promise<any> {
    console.log('Fetching quarterly DCF data for', entityFilteredDataPoints.length, 'data points');
    console.log('Processing periods:', valid_periods);

    const periodData = valid_periods.map(period => {
      // Extract quarter and year from period (e.g., "Q1-2025")
      const [quarter, year] = period.split('-');
      const quarterNum = parseInt(quarter.replace('Q', ''));

      // Define quarter month ranges
      const quarterRanges = {
        1: {start: 1, end: 3},   // Q1: Jan-Mar
        2: {start: 4, end: 6},   // Q2: Apr-Jun
        3: {start: 7, end: 9},   // Q3: Jul-Sep
        4: {start: 10, end: 12}  // Q4: Oct-Dec
      };

      const range = quarterRanges[quarterNum as keyof typeof quarterRanges];

      // Filter data points that fall within this quarter using rp property
      const periodDataPoints = entityFilteredDataPoints.filter((dataPoint: any) => {
        if (!dataPoint.rp || !Array.isArray(dataPoint.rp)) return false;

        // Generate all months in this quarter
        const quarterMonths: string[] = [];
        for (let month = range.start; month <= range.end; month++) {
          quarterMonths.push(`${month.toString().padStart(2, '0')}-${year}`);
        }

        // Check if rp contains months from this quarter
        const rpMonthsInPeriod = dataPoint.rp.filter((rpMonth: string) => quarterMonths.includes(rpMonth));

        // Must have at least one month from this period
        if (rpMonthsInPeriod.length === 0) return false;

        // Check if rp spans multiple quarters (invalid)
        const quartersSpanned = new Set<number>();
        for (const rpMonth of dataPoint.rp) {
          const [monthStr, yearStr] = rpMonth.split('-');
          if (yearStr !== year) continue; // Different year, ignore

          const monthNum = parseInt(monthStr);
          // Find which quarter this month belongs to
          if (monthNum >= 1 && monthNum <= 3) quartersSpanned.add(1);
          else if (monthNum >= 4 && monthNum <= 6) quartersSpanned.add(2);
          else if (monthNum >= 7 && monthNum <= 9) quartersSpanned.add(3);
          else if (monthNum >= 10 && monthNum <= 12) quartersSpanned.add(4);
        }

        // Valid if rp doesn't span multiple quarters
        return quartersSpanned.size <= 1;
      });

      return {
        period,
        period_label: `${quarter} ${year} (${this.getMonthName(range.start)}-${this.getMonthName(range.end)})`,
        data_count: periodDataPoints.length,
        data: periodDataPoints
      };
    });

    return {
      type: 'quarterly',
      total_periods: valid_periods.length,
      period_data: periodData,
      total_data_points: entityFilteredDataPoints.length
    };
  }

  // Examples: ["H1-2025"] or ["H2-2025"] or ["H1-2025", "H2-2025"]
  private async fetchHalfYearlyDcfData(entityFilteredDataPoints: any[], valid_periods: string[]): Promise<any> {
    console.log('Fetching half-yearly DCF data for', entityFilteredDataPoints.length, 'data points');
    console.log('Processing periods:', valid_periods);

    const periodData = valid_periods.map(period => {
      // Extract half and year from period (e.g., "H1-2025")
      const [half, year] = period.split('-');
      const halfNum = parseInt(half.replace('H', ''));

      // Define half-year month ranges
      const halfRanges = {
        1: {start: 1, end: 6},   // H1: Jan-Jun
        2: {start: 7, end: 12}   // H2: Jul-Dec
      };

      const range = halfRanges[halfNum as keyof typeof halfRanges];

      // Filter data points that fall within this half-year using rp property
      const periodDataPoints = entityFilteredDataPoints.filter((dataPoint: any) => {
        if (!dataPoint.rp || !Array.isArray(dataPoint.rp)) return false;

        // Generate all months in this half-year
        const halfYearMonths: string[] = [];
        for (let month = range.start; month <= range.end; month++) {
          halfYearMonths.push(`${month.toString().padStart(2, '0')}-${year}`);
        }

        // Check if rp contains months from this half-year
        const rpMonthsInPeriod = dataPoint.rp.filter((rpMonth: string) => halfYearMonths.includes(rpMonth));

        // Must have at least one month from this period
        if (rpMonthsInPeriod.length === 0) return false;

        // Check if rp spans multiple half-years (invalid)
        const halfYearsSpanned = new Set<number>();
        for (const rpMonth of dataPoint.rp) {
          const [monthStr, yearStr] = rpMonth.split('-');
          if (yearStr !== year) continue; // Different year, ignore

          const monthNum = parseInt(monthStr);
          // Find which half-year this month belongs to
          if (monthNum >= 1 && monthNum <= 6) halfYearsSpanned.add(1);
          else if (monthNum >= 7 && monthNum <= 12) halfYearsSpanned.add(2);
        }

        // Valid if rp doesn't span multiple half-years
        return halfYearsSpanned.size <= 1;
      });

      return {
        period,
        period_label: `${half} ${year} (${this.getMonthName(range.start)}-${this.getMonthName(range.end)})`,
        data_count: periodDataPoints.length,
        data: periodDataPoints
      };
    });

    return {
      type: 'half-yearly',
      total_periods: valid_periods.length,
      period_data: periodData,
      total_data_points: entityFilteredDataPoints.length
    };
  }

  // Examples: ["2025"] or ["2024", "2025"] or ["2023", "2024", "2025"]
  private async fetchYearlyDcfData(entityFilteredDataPoints: any[], valid_periods: string[]): Promise<any> {
    console.log('Fetching yearly DCF data for', entityFilteredDataPoints.length, 'data points');
    console.log('Processing periods:', valid_periods);

    const periodData = valid_periods.map(period => {
      // Period is just the year (e.g., "2025")
      const year = parseInt(period);

      // Filter data points that fall within this year using rp property
      const periodDataPoints = entityFilteredDataPoints.filter((dataPoint: any) => {
        if (!dataPoint.rp || !Array.isArray(dataPoint.rp)) return false;

        // Generate all months in this year and check if any exist in rp array
        const yearMonths = [];
        for (let month = 1; month <= 12; month++) {
          yearMonths.push(`${month.toString().padStart(2, '0')}-${year}`);
        }

        // Check if any year month exists in the rp array
        return yearMonths.some(monthPeriod => dataPoint.rp.includes(monthPeriod));
      });

      return {
        period,
        period_label: `Year ${year}`,
        data_count: periodDataPoints.length,
        data: periodDataPoints
      };
    });

    return {
      type: 'yearly',
      total_periods: valid_periods.length,
      period_data: periodData,
      total_data_points: entityFilteredDataPoints.length
    };
  }

  private resolveReportingFrequency(
    input: ReportingFrequencyInput,
    assignment: any
  ): {isValid: boolean; periods?: ReportingPeriod; error?: string} {
    try {
      // First validate frequency compatibility
      const frequencyValidation = this.validateFrequencyCompatibility(
        input.reporting_period,
        assignment,
        input.reporting_period_from,
        input.reporting_period_to,
        input.year
      );

      if (!frequencyValidation.isValid) {
        return {
          isValid: false,
          error: frequencyValidation.incompatibleReason
        };
      }

      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      let periods: ReportingPeriod;

      // Handle custom period
      if (input.reporting_period.toLowerCase() === 'custom') {
        if (!input.reporting_period_from || !input.reporting_period_to) {
          return {
            isValid: false,
            error: 'Custom period requires both start and end dates'
          };
        }

        const startDate = new Date(input.reporting_period_from);
        const endDate = new Date(input.reporting_period_to);
        const validPeriods: string[] = [];

        let currentDate = new Date(startDate);
        while (currentDate <= endDate) {
          const year = currentDate.getFullYear();
          const month = currentDate.getMonth();
          validPeriods.push(`${year}-${(month + 1).toString().padStart(2, '0')}`);
          currentDate.setMonth(currentDate.getMonth() + 1);
        }

        periods = {
          valid_periods: validPeriods,
          data_granularity: input.reporting_frequency
        };
      } else {
        // Handle standard periods
        const yearValue = Array.isArray(input.year) ? input.year[0] : input.year;
        // Use the requested reporting period instead of assignment frequency
        const requestedPeriod = input.reporting_period.toLowerCase();

        switch (requestedPeriod) {
          case 'monthly':
            periods = {
              valid_periods: months.map((_month, index) =>
                `${yearValue}-${(index + 1).toString().padStart(2, '0')}`
              ),
              data_granularity: 'monthly'
            };
            break;

          case 'bi-monthly':
            periods = {
              valid_periods: Array.from({length: 6}, (_, i) =>
                `BM${i + 1}-${yearValue}`
              ),
              data_granularity: 'bi-monthly'
            };
            break;

          case 'quarterly':
            periods = {
              valid_periods: ['Q1', 'Q2', 'Q3', 'Q4'].map(q => `${q}-${yearValue}`),
              data_granularity: 'quarterly'
            };
            break;

          case 'half-yearly':
            periods = {
              valid_periods: ['H1', 'H2'].map(h => `${h}-${yearValue}`),
              data_granularity: 'half-yearly'
            };
            break;

          case 'yearly':
            periods = {
              valid_periods: [yearValue.toString()],
              data_granularity: 'yearly'
            };
            break;

          default:
            return {
              isValid: false,
              error: `Unsupported reporting period: ${input.reporting_period}`
            };
        }
      }

      return {
        isValid: true,
        periods
      };

    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown error in resolving reporting frequency'
      };
    }
  }

  formatDateToMonthYear(date: any) {
    // Get month short name (e.g., Jan, Feb, etc.)
    const monthShort = date.toLocaleString("en-US", {month: "short"});
    // Get year
    const year = date.getFullYear();
    // Combine
    return `${monthShort}-${year}`;
  }

  /**
   * Process data based on type_of_format
   * @param type_of_format - Format type: 'value_field', 'tabular_form_data', 'chart_data'
   * @param table_config - Table configuration object
   * @param chart_config - Chart configuration object
   * @param data_sets - Calculated data sets array
   * @param valid_periods - Valid periods array
   * @param reporting_period - Reporting period type
   * @param period_data - Period data arrays
   * @param dcfIds - DCF IDs array
   * @param entity - Entity array
   * @param sub_data_source - Sub data source type
   * @param raw_parameters - Raw parameters
   * @param indicator_parameters - Indicator parameters
   */
  private async type_of_format(
    type_of_format: string,
    table_config: any,
    chart_config: any,
    data_sets: number[],
    valid_periods: string[],
    reporting_period: string,
    period_data: any[][],
    dcfIds: number[],
    entity: string[],
    sub_data_source: string,
    raw_parameters: any,
    indicator_parameters: any
  ): Promise<any> {
    try {
      switch (type_of_format) {
        case 'value_field':
          // Return single reduced value from all data_sets
          const totalValue = data_sets.reduce((sum, value) => sum + value, 0);
          return {
            format_type: 'value_field',
            value: totalValue
          };

        case 'tabular_form_data':
          return await this.processTabularFormat(
            table_config,
            data_sets,
            valid_periods,
            reporting_period,
            period_data,
            dcfIds,
            entity,
            sub_data_source,
            raw_parameters,
            indicator_parameters
          );

        case 'chart_data':
          return await this.processChartFormat(
            chart_config,
            data_sets,
            valid_periods,
            reporting_period,
            period_data,
            dcfIds,
            entity,
            sub_data_source,
            raw_parameters,
            indicator_parameters
          );

        default:
          return {
            format_type: 'unknown',
            error: `Unsupported format type: ${type_of_format}`
          };
      }
    } catch (error) {
      return {
        format_type: 'error',
        error: `Error processing format: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Process tabular format data
   */
  private async processTabularFormat(
    table_config: any,
    data_sets: number[],
    valid_periods: string[],
    reporting_period: string,
    period_data: any[][],
    dcfIds: number[],
    entity: string[],
    sub_data_source: string,
    raw_parameters: any,
    indicator_parameters: any
  ): Promise<any> {
    try {
      const result: any = {
        format_type: 'tabular_form_data'
      };

      // Always include period_breakdown as default
      result.period_breakdown = this.generatePeriodBreakdown(data_sets, valid_periods, reporting_period);

      // Add entity_breakdown only if enabled
      if (table_config?.entity_breakdown) {
        result.entity_breakdown = await this.generateEntityBreakdown(
          data_sets,
          valid_periods,
          reporting_period,
          period_data,
          entity
        );
      }

      // Add dcf_breakdown only if enabled
      if (table_config?.dcf_breakdown) {
        result.dcf_breakdown = await this.generateDcfBreakdown(
          data_sets,
          valid_periods,
          reporting_period,
          period_data,
          dcfIds,
          sub_data_source,
          raw_parameters,
          indicator_parameters
        );
      }

      return result;

    } catch (error) {
      return {
        format_type: 'tabular_error',
        error: `Error processing tabular format: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Process chart format data (similar to tabular but for charts)
   */
  private async processChartFormat(
    chart_config: any,
    data_sets: number[],
    valid_periods: string[],
    reporting_period: string,
    period_data: any[][],
    dcfIds: number[],
    entity: string[],
    sub_data_source: string,
    raw_parameters: any,
    indicator_parameters: any
  ): Promise<any> {
    try {
      const result: any = {
        format_type: 'chart_data'
      };

      // Always include period_breakdown as default
      result.period_breakdown = this.generatePeriodBreakdown(data_sets, valid_periods, reporting_period);

      // Add entity_breakdown only if enabled
      if (chart_config?.entity_breakdown) {
        result.entity_breakdown = await this.generateEntityBreakdown(
          data_sets,
          valid_periods,
          reporting_period,
          period_data,
          entity
        );
      }

      // Add dcf_breakdown only if enabled
      if (chart_config?.dcf_breakdown) {
        result.dcf_breakdown = await this.generateDcfBreakdown(
          data_sets,
          valid_periods,
          reporting_period,
          period_data,
          dcfIds,
          sub_data_source,
          raw_parameters,
          indicator_parameters
        );
      }

      return result;

    } catch (error) {
      return {
        format_type: 'chart_error',
        error: `Error processing chart format: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Generate period breakdown table
   */
  private generatePeriodBreakdown(
    data_sets: number[],
    valid_periods: string[],
    reporting_period: string
  ): any {
    // Convert periods to column headers
    const columns = valid_periods.map(period => this.formatPeriodForDisplay(period));

    return {
      table_type: 'period_breakdown',
      reporting_period: reporting_period.toLowerCase(),
      columns,
      rows: [
        {
          label: 'Total Consumption',
          values: data_sets
        }
      ]
    };
  }

  /**
   * Generate entity breakdown table
   */
  private async generateEntityBreakdown(
    _data_sets: number[],
    valid_periods: string[],
    reporting_period: string,
    period_data: any[][],
    entity: string[]
  ): Promise<any> {
    try {
      // Get entity details using the provided query
      const filteredLocations = await this.userProfileRepository.locationOnes(94).find({
        include: [
          {
            relation: "locationTwos",
            scope: {
              include: [{relation: "locationThrees"}],
            },
          },
        ],
      });

      const shapedSite = filteredLocations.map(item => {
        if (item.locationTwos) {
          item.locationTwos = item.locationTwos.filter(locationTwo =>
            locationTwo.locationThrees && locationTwo.locationThrees.length > 0
          );
        }
        return item;
      }).filter(item => item.locationTwos && item.locationTwos.length > 0);

      // Create entity name mapping
      const entityNameMap = new Map<string, string>();
      shapedSite.forEach(location => {
        const level1Key = `1-${location.id}`;
        entityNameMap.set(level1Key, location.name || '');

        location.locationTwos?.forEach((locationTwo: any) => {
          const level2Key = `2-${locationTwo.id}`;
          entityNameMap.set(level2Key, locationTwo.name || '');

          locationTwo.locationThrees?.forEach((locationThree: any) => {
            const level3Key = `3-${locationThree.id}`;
            entityNameMap.set(level3Key, locationThree.name || '');
          });
        });
      });

      // Convert periods to column headers
      const columns = valid_periods.map(period => this.formatPeriodForDisplay(period));

      // Generate rows for each entity
      const rows = entity.map(entityKey => {
        const entityName = entityNameMap.get(entityKey) || entityKey;

        // Calculate values for each period for this entity
        const values = valid_periods.map((_period, periodIndex) => {
          const periodDataArray = period_data[periodIndex] || [];

          // Filter data for this specific entity
          const entityData = periodDataArray.filter((dataPoint: any) => {
            const dataEntityKey = `${dataPoint.level}-${dataPoint.locationId}`;
            return dataEntityKey === entityKey;
          });

          // Sum computedValue for this entity in this period
          return entityData.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);
        });

        return {
          entity: entityName,
          values
        };
      });

      return {
        table_type: 'entity_breakdown',
        reporting_period: reporting_period.toLowerCase(),
        columns,
        rows
      };

    } catch (error) {
      console.error('Error generating entity breakdown:', error);
      return {
        error: `Error generating entity breakdown: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Generate DCF breakdown table
   */
  private async generateDcfBreakdown(
    _data_sets: number[],
    valid_periods: string[],
    _reporting_period: string,
    period_data: any[][],
    dcfIds: number[],
    sub_data_source: string,
    _raw_parameters: any,
    indicator_parameters: any
  ): Promise<any> {
    try {
      // Convert periods to column headers
      const columns = valid_periods.map(period => this.formatPeriodForDisplay(period));

      if (sub_data_source === 'raw') {
        // Get DCF names from formCollectionRepository
        const dcfForms = await this.formCollectionRepository.find({
          where: {
            id: {inq: dcfIds}
          },
          fields: ['id', 'title']
        });

        const dcfNameMap = new Map<number, string>();
        dcfForms.forEach(form => {
          if (form.id !== undefined) {
            dcfNameMap.set(form.id, form.title || '');
          }
        });

        // Generate rows for each data point (assuming raw data has data points)
        const dataPointMap = new Map<string, number[]>();

        // Process period data to extract data points using title
        period_data.forEach((periodDataArray, periodIndex) => {
          periodDataArray.forEach((dataPoint: any) => {
            const dataPointName = dataPoint.title || dataPoint.dataPointName || dataPoint.name || 'Unknown';

            if (!dataPointMap.has(dataPointName)) {
              dataPointMap.set(dataPointName, new Array(valid_periods.length).fill(0));
            }

            const values = dataPointMap.get(dataPointName)!;
            values[periodIndex] += parseFloat(dataPoint.computedValue) || 0;
          });
        });

        const rows = Array.from(dataPointMap.entries()).map(([dataPointName, values]) => ({
          data_point: dataPointName,
          values
        }));

        return {
          table_type: 'dcf_breakdown',
          dcf_name: dcfForms.length > 0 ? dcfForms[0].title : 'Unknown DCF',
          columns,
          rows
        };

      } else if (sub_data_source === 'indicator') {
        // For indicator data source, show DCF breakdown
        const dcfForms = await this.formCollectionRepository.find({
          where: {
            id: {inq: dcfIds}
          },
          fields: ['id', 'title']
        });

        const dcfNameMap = new Map<number, string>();
        dcfForms.forEach(form => {
          if (form.id !== undefined) {
            dcfNameMap.set(form.id, form.title || '');
          }
        });

        // Generate rows for each DCF
        const rows = dcfIds.map(dcfId => {
          const dcfName = dcfNameMap.get(dcfId) || `DCF ${dcfId}`;

          // Calculate values for each period for this DCF
          const values = valid_periods.map((_period, periodIndex) => {
            const periodDataArray = period_data[periodIndex] || [];

            // Filter data for this specific DCF
            const dcfData = periodDataArray.filter((dataPoint: any) => dataPoint.dcfId === dcfId);

            // Sum computedValue for this DCF in this period
            return dcfData.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);
          });

          return {
            dcf_name: dcfName,
            values
          };
        });

        // Get indicator name
        let indicatorName = 'Unknown Indicator';
        if (indicator_parameters?.indicator_name && indicator_parameters.indicator_name.length > 0) {
          try {
            const indicatorList = await this.userProfileController.getAssignedIndicator(94, {
              indicatorId: indicator_parameters.indicator_name
            });
            if (indicatorList.length > 0) {
              indicatorName = indicatorList[0].name || 'Unknown Indicator';
            }
          } catch (error) {
            console.error('Error getting indicator name:', error);
          }
        }

        return {
          table_type: 'dcf_breakdown',
          indicator_name: indicatorName,
          columns,
          rows
        };
      }

      // Fallback
      return {
        error: 'Unsupported sub_data_source for DCF breakdown'
      };

    } catch (error) {
      console.error('Error generating DCF breakdown:', error);
      return {
        error: `Error generating DCF breakdown: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Format period for display in table columns
   */
  private formatPeriodForDisplay(period: string): string {
    // Handle different period formats
    if (period.includes('-') && period.match(/^\d{4}-\d{2}$/)) {
      // Monthly format: "2025-01" -> "Jan"
      const [_year, month] = period.split('-');
      return this.getMonthName(parseInt(month));
    } else if (period.includes('Q')) {
      // Quarterly format: "Q1-2025" -> "Q1"
      return period.split('-')[0];
    } else if (period.includes('H')) {
      // Half-yearly format: "H1-2025" -> "H1"
      return period.split('-')[0];
    } else if (period.includes('BM')) {
      // Bi-monthly format: "BM1-2025" -> "BM1"
      return period.split('-')[0];
    } else if (period.match(/^\d{4}$/)) {
      // Yearly format: "2025" -> "2025"
      return period;
    }

    // Default fallback
    return period;
  }

}
