import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {SapResponse} from '../models';
import {NewEfItemRepository, SapResponseRepository, UserProfileRepository} from '../repositories';
import {SapService} from '../services/sap.service';
const FY25Months = ['Apr-2024', 'May-2024', 'Jun-2024', 'Jul-2024', 'Aug-2024', 'Sep-2024', 'Oct-2024', 'Nov-2024', 'Dec-2024', 'Jan-2025', 'Feb-2025', 'Mar-2025'];

export class SapResponseController {
  constructor(
    @repository(SapResponseRepository)
    public sapResponseRepository: SapResponseRepository,
    @repository(NewEfItemRepository)
    public newEfItemRepository: NewEfItemRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
    @inject('services.S3Service') private s3Service: SapService,

  ) { }

  @post('/sap-responses', {
    responses: {
      '200': {
        description: 'SapResponse model instance',
        content: {'application/json': {schema: getModelSchemaRef(SapResponse)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SapResponse, {
            title: 'NewSapResponse',
            exclude: ['id'],
          }),
        },
      },
    })
    sapResponse: Omit<SapResponse, 'id'>,
  ): Promise<SapResponse> {
    return this.sapResponseRepository.create(sapResponse);
  }

  @get('/sap-responses/count', {
    responses: {
      '200': {
        description: 'SapResponse model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(SapResponse) where?: Where<SapResponse>,
  ): Promise<Count> {
    return this.sapResponseRepository.count(where);
  }

  @get('/sap-responses', {
    responses: {
      '200': {
        description: 'Array of SapResponse model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(SapResponse, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(SapResponse) filter?: Filter<SapResponse>,
  ): Promise<SapResponse[]> {
    return this.sapResponseRepository.find(filter);
  }

  @patch('/sap-responses', {
    responses: {
      '200': {
        description: 'SapResponse PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SapResponse, {partial: true}),
        },
      },
    })
    sapResponse: SapResponse,
    @param.where(SapResponse) where?: Where<SapResponse>,
  ): Promise<Count> {
    return this.sapResponseRepository.updateAll(sapResponse, where);
  }

  @get('/sap-responses/{id}', {
    responses: {
      '200': {
        description: 'SapResponse model instance',
        content: {
          'application/json': {
            schema: getModelSchemaRef(SapResponse, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SapResponse, {exclude: 'where'}) filter?: FilterExcludingWhere<SapResponse>
  ): Promise<SapResponse> {
    return this.sapResponseRepository.findById(id, filter);
  }

  @patch('/sap-responses/{id}', {
    responses: {
      '204': {
        description: 'SapResponse PATCH success',
      },
    },
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SapResponse, {partial: true}),
        },
      },
    })
    sapResponse: SapResponse,
  ): Promise<void> {
    await this.sapResponseRepository.updateById(id, sapResponse);
  }

  @put('/sap-responses/{id}', {
    responses: {
      '204': {
        description: 'SapResponse PUT success',
      },
    },
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() sapResponse: SapResponse,
  ): Promise<void> {
    await this.sapResponseRepository.replaceById(id, sapResponse);
  }

  @del('/sap-responses/{id}', {
    responses: {
      '204': {
        description: 'SapResponse DELETE success',
      },
    },
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.sapResponseRepository.deleteById(id);
  }


  @get('/scope1-fuel-responses', {
    responses: {
      '200': {
        description: 'Array of SapResponse model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(SapResponse, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async findFuelResponse(
    @param.filter(SapResponse) filter?: Filter<SapResponse>,
  ): Promise<any> {
    const SapResponseData = await this.sapResponseRepository.find({
      where: {sapId: 'SAP1'},
      fields: {
        id: true,
        sapId: true,
        Location: true,
        Date: true,
        FuelType: true,
        Quantity: true,
        UoM: true,
        tier0_id: true,
        tier1_id: true,
        tier2_id: true,
        tier3_id: true,
      },
    });

    const fuelSubcategories: Record<string, {subcategory1: number; subcategory2: number; subcategory3: number}> = {
      petrol: {subcategory1: 4, subcategory2: 19, subcategory3: 75},
      hsd: {subcategory1: 4, subcategory2: 13, subcategory3: 51},
      propane: {subcategory1: 1, subcategory2: 8, subcategory3: 30},
      lpg: {subcategory1: 1, subcategory2: 4, subcategory3: 14},
      'furnace oil / hfo': {subcategory1: 4, subcategory2: 14, subcategory3: 55},
    };

    const newEfId = 38;

    // 🔁 Preload all newEfItems for the EF lookup
    const efItems = await this.newEfItemRepository.find({
      where: {newEfId}
    });

    // 🔁 Build a lookup map: "1-2-3" => efItem
    const efMap = new Map<string, any>();
    for (const ef of efItems) {
      const key = `${ef.subcategory1}-${ef.subcategory2}-${ef.subcategory3}`;
      efMap.set(key, ef);
    }

    const grouped: Record<string, any> = {};

    for (const item of SapResponseData) {
      const monthYear = DateTime.fromFormat(item?.Date || '', 'yyyyMMdd').toFormat('MM-yyyy');

      // ✅ Removed location from the grouping key
      const key = `${monthYear}_${item.FuelType}_${item.UoM}_${item.tier0_id}_${item.tier1_id}_${item.tier2_id}_${item.tier3_id}`;

      if (!grouped[key]) {
        grouped[key] = {
          location: item.Location ?? 'Unknown',
          date: [monthYear],
          sapId: item.sapId ?? '',
          FuelType: item.FuelType ?? '',
          UoM: item.UoM ?? '',
          tier0_id: String(item.tier0_id ?? ''),
          tier1_id: String(item.tier1_id ?? ''),
          tier2_id: String(item.tier2_id ?? ''),
          tier3_id: String(item.tier3_id ?? ''),
          totalQuantity: 0,
          kgCO2: 0,
          tonnesCO2: 0,
          newEfId,
          co2e: 0,
          co2: 0,
          ch4: 0,
          n2o: 0,
          methodology: '',
          unit: '',
          efKey: '',
          emissionFactorName: 'DEFRA'
        };
      }

      grouped[key].totalQuantity += item.Quantity;

      const fuelTypeLower = item.FuelType?.toLowerCase() ?? '';
      if (fuelSubcategories[fuelTypeLower]) {
        const {subcategory1, subcategory2, subcategory3} = fuelSubcategories[fuelTypeLower];
        const efKeyLookup = `${subcategory1}-${subcategory2}-${subcategory3}`;
        const efItem = efMap.get(efKeyLookup);

        if (efItem?.co2e != null) {
          let kgCO2 = 0;
          let tonnesCO2 = 0;

          if (item.UoM === 'L') {
            kgCO2 = item.Quantity * efItem.co2e;
            tonnesCO2 = kgCO2 / 1000;
          } else if (item.UoM === 'KG' || item.UoM === 'kg') {
            kgCO2 = (item.Quantity / 1000) * efItem.co2e;
            tonnesCO2 = kgCO2 / 1000;
          }

          grouped[key].kgCO2 += kgCO2;
          grouped[key].tonnesCO2 += tonnesCO2;

          grouped[key].co2e = efItem.co2e ?? 0;
          grouped[key].co2 = efItem.co2 ?? 0;
          grouped[key].ch4 = efItem.ch4 ?? 0;
          grouped[key].n2o = efItem.n2o ?? 0;
          grouped[key].methodology = efItem.methodology ?? '';
          grouped[key].unit = efItem.unit ?? '';
          grouped[key].efKey = `S1-T21-G1-GS1-I1-${subcategory1}-${subcategory2}-${subcategory3}`;
        }
      }
    }

    const result = Object.values(grouped);

    // 🔄 Sort by date (ascending)
    result.sort((a, b) => {
      const aDate = DateTime.fromFormat(a.date[0], 'MM-yyyy');
      const bDate = DateTime.fromFormat(b.date[0], 'MM-yyyy');
      return aDate.valueOf() - bDate.valueOf();
    });

    // ✅ Transform the result
    const transformed = result.map(item => {
      const reportingDate = [DateTime.fromFormat(item.date[0], 'MM-yyyy').toFormat('MM-yyyy')];

      return {
        location: item.location,
        periodFrom: item.date[0],
        periodTo: item.date[0],
        sapId: item.sapId,
        title: item.FuelType,
        unitOfMeasure: item.UoM,
        tier0_id: item.tier0_id,
        tier1_id: item.tier1_id,
        tier2_id: item.tier2_id,
        tier3_id: item.tier3_id,
        value: parseFloat(item.totalQuantity.toFixed(3)),
        kgCO2: parseFloat(item.kgCO2.toFixed(3)),
        computedValue: parseFloat(item.tonnesCO2.toFixed(3)),
        newEfId: item.newEfId,
        emissionFactorValue: item.co2e,
        co2: item.co2,
        ch4: item.ch4,
        n2o: item.n2o,
        methodology: `(Fuel Consumption * Emission Factors based on the Fuel used) / 1000`,
        unit: item.unit,
        efKey: item.efKey,
        emissionFactorName: item.emissionFactorName,
        status: 'Approved',
        reportingDate: reportingDate,
      };
    });

    return transformed;
  }

  @post('/migrate-fuel-data')
  @response(200, {
    description: 'Migrate and club fuel data for SAP1 - deletes existing SAP1 data and creates new clubbed records',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            processedCount: {type: 'number'},
            clubbedCount: {type: 'number'},
            deletedCount: {type: 'number'},
            createdCount: {type: 'number'}
          }
        }
      }
    }
  })
  async migrateFuelData(): Promise<any> {
    try {
      const sapId = 'SAP1'; // Fixed to SAP1

      // Fetch raw SAP response data
      const rawData = await this.sapResponseRepository.find({
        where: {sapId: 'SAP1'},
        fields: {
          id: true,
          sapId: true,
          Location: true,
          level: true,
          Plant: true, locationId: true, Title: true,
          Date: true,
          FuelType: true,
          Quantity: true,
          UoM: true,
          tier0_id: true,
          tier1_id: true,
          tier2_id: true,
          tier3_id: true,
          dataType: true,
          userProfileId: true,
          fetched_on: true
        },
      });

      if (rawData.length === 0) {
        return {
          status: false,
          message: `No data found for sapId: ${sapId}`,
          processedCount: 0,
          clubbedCount: 0,
          deletedCount: 0,
          createdCount: 0
        };
      }

      // Fuel subcategories mapping for efKey generation
      const fuelSubcategories: Record<string, {subcategory1: number; subcategory2: number; subcategory3: number}> = {
        petrol: {subcategory1: 4, subcategory2: 19, subcategory3: 75},
        hsd: {subcategory1: 4, subcategory2: 13, subcategory3: 51},
        propane: {subcategory1: 1, subcategory2: 8, subcategory3: 30},
        lpg: {subcategory1: 1, subcategory2: 4, subcategory3: 14},
        'furnace oil / hfo': {subcategory1: 4, subcategory2: 14, subcategory3: 55},
      };

      // Group data by Month, FuelType, UoM, tier_id, tier2_id, tier3_id
      const grouped: Record<string, any> = {};

      for (const item of rawData) {
        // Convert Date (yyyyMMdd) to Month (MM-yyyy)
        const monthYear = DateTime.fromFormat(item?.Date || '', 'yyyyMMdd').toFormat('LLL-yyyy');
        const Date = DateTime.fromFormat(monthYear, 'LLL-yyyy').startOf('month').toFormat('yyyyMMdd');
        // Create grouping key
        const key = `${monthYear}_${item.FuelType}_${item.UoM}_${item.tier0_id}_${item.tier1_id}_${item.tier2_id}_${item.tier3_id}`;

        if (!grouped[key] && item.locationId != null && item.Plant && item.Location && item.level != null) {

          grouped[key] = {
            Month: monthYear,
            Quantity: 0,
            Date: Date,
            tier0_id: item.tier0_id ?? null,
            tier1_id: item.tier1_id ?? null,
            tier2_id: item.tier2_id ?? null,
            tier3_id: item.tier3_id ?? null,
            efKey: '',
            // Common properties
            Title: 'Fuel',
            sapId: 'SAP1',
            dataType: 1,
            Plant: item.Plant,
            userProfileId: 289,
            locationId: item.locationId, // Use tier1_id as locationId
            level: item.level,
            Location: item.Location,
            fetched_on: DateTime.utc().toString(),
            // Additional properties from first object
            fuelType: item.FuelType ?? '',
            uom: item.UoM ?? ''
          };
        }
        console.log(item, key)
        // Sum the values
        grouped[key].Quantity += (item.Quantity || 0);
      }

      // Update efKey for each grouped item
      const clubbedData = Object.values(grouped);

      for (const item of clubbedData) {
        const fuelTypeLower = item.fuelType?.toLowerCase() ?? '';
        if (fuelSubcategories[fuelTypeLower]) {
          const {subcategory1, subcategory2, subcategory3} = fuelSubcategories[fuelTypeLower];
          item.efKey = `${subcategory1}-${subcategory2}-${subcategory3}`;
        } else {
          item.efKey = 'UNKNOWN_FUEL_TYPE';
        }

        // Keep fuelType and uom in the final records for reference
        // Rename to match expected field names if needed
        item.FuelType = item.fuelType;
        item.UoM = item.uom;

        // Remove temporary lowercase properties
        delete item.fuelType;
        delete item.uom;
      }

      // Step 1: Delete all existing SAP1 data
      const deleteResult = await this.sapResponseRepository.deleteAll({sapId: 'SAP1'});
      const deletedCount = deleteResult.count || 0;

      // Step 2: Create new records with clubbed data
      const createdRecords = await this.sapResponseRepository.createAll(clubbedData);
      const createdCount = createdRecords.length;

      return {
        status: true,
        message: `Migration completed successfully for ${sapId}. Deleted ${deletedCount} existing records and created ${createdCount}  new clubbed records.`,
        processedCount: rawData.length,
        clubbedCount: clubbedData,
        summary: {
          totalOriginalRecords: rawData.length,
          totalClubbedRecords: clubbedData.length,
          reductionPercentage: ((rawData.length - clubbedData.length) / rawData.length * 100).toFixed(2) + '%'
        }
      };

    } catch (error) {
      console.error('Error in migrateFuelData:', error);
      return {
        status: false,
        message: `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        processedCount: 0,
        clubbedCount: 0,
        updatedCount: 0
      };
    }
  }

  @post('/purchase-goods-multiple')
  @response(200,
    {
      description: 'Array of yourModel model instances',
      content: {
        'application/json': {
          schema: {
            type: 'array',
            items: {'x-ts-type': SapResponse},
          },
        },
      }
    }
  )
  async updatePurchaseGoodsFromArray(
    @requestBody() dataArray: SapResponse[],
  ): Promise<{}> {
    try {
      await this.s3Service.pushUpdatedOnly(dataArray.filter((x: any) => (x.TotalSpent != null) && x?.MaterialCategory && x?.Month && FY25Months.includes(x?.Month)).map((x: any) => ({TotalSpent: x.TotalSpent, tier1_id: 103, level: 1, locationId: 103, Plant: 'India', Location: 'India', Month: x.Month, Date: DateTime.fromFormat(x.Month, 'LLL-yyyy').toFormat('yyyyMMdd'), MaterialCategory: x.MaterialCategory, sapId: "SAP4", dataType: 4, UoM: 'USD', userProfileId: 289, Title: 'Purchase Goods & Services', fetched_on: DateTime.utc().toString(), })), 5000, 'SAP4');

    } catch (e) {
      return {status: false, message: e, length: dataArray.length}
    } finally {
      return {status: true, length: dataArray.length}
    }


  }
  @post('user-profiles/{id}/fetch-sap-response-custom')
  async fetchResponse(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              startDate: {
                type: 'string',
              },
              endDate: {
                type: 'string',
              }

            }
          }
        }
      },
    })
    requestBody: any, @param.path.number('id') id: number,
    @param.filter(SapResponse) filter?: Filter<SapResponse>): Promise<any> {
    try {

      const data = await this.userProfileRepository.sapResponses(id).find(filter);
      if (requestBody?.startDate && requestBody?.endDate) {
        const filteredData = await this.filterByDateRange(requestBody.startDate, requestBody.endDate, data);
        return {result: true, data: filteredData}
      } else {
        return {result: true, data: data}
      }

    } catch (e) {
      console.error('Error fetching data:', e);
      return {result: false, data: []}
    }
  }
  async filterByDateRange(start: string, end: string, data: any[]) {
    // Convert start and end range to DateTime objects
    const startDate = DateTime.fromFormat(start, 'ddMyyyy')
    const endDate = DateTime.fromFormat(end, 'ddMyyyy')



    // Filter the data
    const filteredData = data.filter((item) => {

      if (item.Date) {
        const itemDate = DateTime.fromFormat(item.Date, 'yyyyMdd');

        return itemDate >= startDate && itemDate <= endDate;
      } else if (item.Date === null) {
        return true
      }
      return false;
    });

    return filteredData;
  }

}
